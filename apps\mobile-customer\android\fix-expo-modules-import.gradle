// Custom Gradle script to fix ExpoModulesPackage import issue
// This script runs before Java compilation to fix the incorrect import path
// Issue: expo.core.ExpoModulesPackage should be expo.modules.ExpoModulesPackage

task fixExpoModulesImport {
    description = 'Fix incorrect ExpoModulesPackage import in generated autolinking files'
    group = 'build setup'
    
    doLast {
        println "🔧 Fixing ExpoModulesPackage import issue..."
        
        // Find all PackageList.java files
        def packageListFiles = []
        
        // Common locations for PackageList.java
        def searchDirs = [
            file("${project.buildDir}/generated/autolinking/src/main/java/com/facebook/react/"),
            file("${project.projectDir}/src/main/java/com/facebook/react/"),
            file("${project.buildDir}/generated/rncli/src/main/java/com/facebook/react/")
        ]
        
        searchDirs.each { dir ->
            if (dir.exists()) {
                dir.traverse(type: groovy.io.FileType.FILES, nameFilter: ~/.*PackageList\.java$/) { file ->
                    packageListFiles.add(file)
                }
            }
        }
        
        // If no files found in common locations, search recursively
        if (packageListFiles.isEmpty()) {
            println "🔍 Searching recursively for PackageList.java files..."
            project.projectDir.traverse(type: groovy.io.FileType.FILES, nameFilter: ~/.*PackageList\.java$/) { file ->
                packageListFiles.add(file)
            }
        }
        
        if (packageListFiles.isEmpty()) {
            println "⚠️  No PackageList.java files found - autolinking may not have run yet"
            return
        }
        
        packageListFiles.each { file ->
            println "📁 Processing: ${file.absolutePath}"
            
            def content = file.text
            def originalContent = content
            
            // Fix the incorrect import
            content = content.replaceAll(
                /import expo\.core\.ExpoModulesPackage;/,
                'import expo.modules.ExpoModulesPackage;'
            )
            
            if (content != originalContent) {
                // Create backup
                def backupFile = new File(file.absolutePath + '.backup')
                backupFile.text = originalContent
                println "💾 Created backup: ${backupFile.absolutePath}"
                
                // Write fixed content
                file.text = content
                println "✅ Fixed import in: ${file.name}"
            } else {
                println "ℹ️  No incorrect import found in: ${file.name}"
            }
        }
        
        println "✅ ExpoModulesPackage import fix completed"
    }
}

// Hook into the build process - run before Java compilation
tasks.whenTaskAdded { task ->
    if (task.name.contains('compileReleaseJavaWithJavac') || 
        task.name.contains('compileDebugJavaWithJavac') ||
        task.name.contains('compileJavaWithJavac')) {
        task.dependsOn fixExpoModulesImport
        println "🔗 Hooked fixExpoModulesImport to run before ${task.name}"
    }
}

// Also try to run after autolinking tasks
tasks.whenTaskAdded { task ->
    if (task.name.contains('generatePackageList') || 
        task.name.contains('autolinking') ||
        task.name.contains('Autolinking')) {
        fixExpoModulesImport.mustRunAfter task
        println "🔗 Configured fixExpoModulesImport to run after ${task.name}"
    }
}
